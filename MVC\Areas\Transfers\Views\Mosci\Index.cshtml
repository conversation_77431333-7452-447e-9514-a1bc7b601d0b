@model Odrdc.Dots.Areas.Transfers.Models.Mosci.MosciPageViewModel
@{
    ViewData["Title"] = "MOSCI - Inmate Schedule";
}

<div class="container-fluid">
    <h2>@ViewData["Title"]</h2>

    @* Display messages *@
    @if (!string.IsNullOrEmpty(Model.Message))
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            @Model.Message
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            @Model.ErrorMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @using (Html.BeginForm("Index", "Mosci", FormMethod.Post, new { @class = "needs-validation", novalidate = "novalidate" }))
    {
        @Html.AntiForgeryToken()

        @* Search Section *@
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Search Inmates</h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-2">
                        @Html.LabelFor(m => m.SearchPrefix, new { @class = "form-label" })
                        @Html.DropDownListFor(m => m.SearchPrefix, Model.PrefixOptions, new { @class = "form-select" })
                    </div>
                    <div class="col-md-4">
                        @Html.LabelFor(m => m.SearchOffenderId, new { @class = "form-label" })
                        @Html.TextBoxFor(m => m.SearchOffenderId, new { @class = "form-control onlyNumeric", @id = "txtInmateNum", @maxlength = "6", @placeholder = "Enter Offender ID" })
                        @Html.ValidationMessageFor(m => m.SearchOffenderId, "", new { @class = "text-danger" })
                    </div>
                    <div class="col-md-6 d-flex align-items-end">
                        <button type="submit" name="submitAction" value="Search" class="btn btn-primary me-2">
                            <i class="fas fa-search"></i> Search
                        </button>
                        <button type="submit" name="submitAction" value="AddNew" class="btn btn-success">
                            <i class="fas fa-plus"></i> Add New Row
                        </button>
                    </div>
                </div>
            </div>
        </div>

        @* Data Grid Section *@
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Inmate Schedule (@Model.Inmates.Count/@Odrdc.Dots.Areas.Transfers.Models.Mosci.MosciPageViewModel.MaxRows rows)</h5>
                <div>
                    <button type="submit" name="submitAction" value="RemoveSelected" class="btn btn-warning btn-sm me-1">
                        <i class="fas fa-minus"></i> Remove Selected
                    </button>
                    <button type="submit" name="submitAction" value="DeleteSelected" class="btn btn-danger btn-sm">
                        <i class="fas fa-trash"></i> Delete Selected
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-striped table-hover mb-0">
                        <thead class="table-dark">
                            <tr>
                                <th style="width: 40px;">Rem</th>
                                <th style="width: 40px;">Del</th>
                                <th style="width: 100px;">Sch Date</th>
                                <th style="width: 80px;">Schd Inst</th>
                                <th style="width: 80px;">Inst No</th>
                                <th style="width: 80px;">OID</th>
                                <th style="width: 200px;">Description</th>
                                <th style="width: 80px;">Status</th>
                                <th style="width: 100px;">Station</th>
                                <th style="width: 100px;">Sys Date</th>
                                <th style="width: 80px;">ROM ID</th>
                                <th style="width: 120px;">Last Name</th>
                                <th style="width: 120px;">First Name</th>
                                <th style="width: 60px;">Prefix</th>
                                <th style="width: 100px;">Offender ID</th>
                            </tr>
                        </thead>
                        <tbody>
                            @for (int i = 0; i < Model.Inmates.Count; i++)
                            {
                                <tr>
                                    <td>
                                        @Html.CheckBoxFor(m => m.Inmates[i].IsMarkedForRemoval, new { @class = "form-check-input" })
                                        @Html.HiddenFor(m => m.Inmates[i].Recno)
                                    </td>
                                    <td>
                                        @Html.CheckBoxFor(m => m.Inmates[i].IsMarkedForDeletion, new { @class = "form-check-input" })
                                    </td>
                                    <td>
                                        @Html.TextBoxFor(m => m.Inmates[i].SchDate, "{0:MM/dd/yyyy}", new { @class = "form-control form-control-sm", @type = "date" })
                                    </td>
                                    <td>
                                        @Html.TextBoxFor(m => m.Inmates[i].SchdInst, new { @class = "form-control form-control-sm" })
                                    </td>
                                    <td>
                                        @Html.TextBoxFor(m => m.Inmates[i].Instno, new { @class = "form-control form-control-sm" })
                                    </td>
                                    <td>
                                        @Html.TextBoxFor(m => m.Inmates[i].Oid, new { @class = "form-control form-control-sm" })
                                    </td>
                                    <td>
                                        @Html.TextBoxFor(m => m.Inmates[i].Descrl, new { @class = "form-control form-control-sm" })
                                    </td>
                                    <td>
                                        @Html.TextBoxFor(m => m.Inmates[i].Sts, new { @class = "form-control form-control-sm" })
                                    </td>
                                    <td>
                                        @Html.TextBoxFor(m => m.Inmates[i].Stationame, new { @class = "form-control form-control-sm" })
                                    </td>
                                    <td>
                                        @Html.TextBoxFor(m => m.Inmates[i].SysDate, "{0:MM/dd/yyyy}", new { @class = "form-control form-control-sm", @type = "date" })
                                    </td>
                                    <td>
                                        @Html.TextBoxFor(m => m.Inmates[i].Romid, new { @class = "form-control form-control-sm" })
                                    </td>
                                    <td>
                                        @Html.TextBoxFor(m => m.Inmates[i].LastName, new { @class = "form-control form-control-sm" })
                                    </td>
                                    <td>
                                        @Html.TextBoxFor(m => m.Inmates[i].FirstName, new { @class = "form-control form-control-sm" })
                                    </td>
                                    <td>
                                        @Html.DropDownListFor(m => m.Inmates[i].InmateIdPrefix, Model.PrefixOptions, new { @class = "form-select form-select-sm" })
                                    </td>
                                    <td>
                                        @Html.TextBoxFor(m => m.Inmates[i].OffenderId, new { @class = "form-control form-control-sm onlyNumeric", @maxlength = "6" })
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between">
                    <div>
                        <small class="text-muted">
                            @if (Model.HasSearchResults)
                            {
                                <span>Showing search results</span>
                            }
                            else
                            {
                                <span>Showing @Model.Inmates.Count of @Odrdc.Dots.Areas.Transfers.Models.Mosci.MosciPageViewModel.MaxRows maximum rows</span>
                            }
                        </small>
                    </div>
                    <div>
                        <button type="submit" name="submitAction" value="Save" class="btn btn-primary me-2">
                            <i class="fas fa-save"></i> Save
                        </button>
                        <button type="submit" name="submitAction" value="Cancel" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@section Scripts {
    <script>
        // Restrict numeric input fields to numbers only
        $(document).ready(function() {
            $('.onlyNumeric').on('input', function() {
                this.value = this.value.replace(/[^0-9]/g, '');
            });
        });
    </script>
}
