@model Odrdc.Dots.Areas.Transfers.Models.Mosci.MosciPageViewModel

@{
    ViewBag.Title = "MOSCI";
}


@if (!string.IsNullOrWhiteSpace(ViewBag.Message))
{
    <div class="alert alert-success fade in">
        <a href="#" class="close" data-dismiss="alert" aria-label="close">&times;</a>
        @ViewBag.Message
    </div>
}
@if (!string.IsNullOrWhiteSpace(ViewBag.ErrorMessage))
{
    <div class="alert alert-danger fade in">
        <a href="#" class="close" data-dismiss="alert" aria-label="close">&times;</a>
        @ViewBag.ErrorMessage
    </div>
}



<div id="divErrorMessage" class="alert alert-danger fade in" style="display:none">
    <a href="#" class="close" data-dismiss="alert" aria-label="close">X</a>
    <span id="ErrorMessage"></span>
</div>

@* Hidden fields for search functionality *@
@Html.HiddenFor(m => m.SearchPrefix)
@Html.HiddenFor(m => m.SearchOffenderId)

@*<div id="Housing-Manage" class="no-print">
        <div class="row">
            <div class="col-md-12">
                <div class="divFindOffender">
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            Find Inmate
                        </div>
                        <div class="panel-body">
                            <div class="form-inline">
                                <div class="form-group col-xs-12 col-sm-6 col-md-6">
                                    @Html.DropDownListFor(m => m.InmateIdPrefix, Model.Prefix, new { @class = "form-control" })
                                    @Html.TextBoxFor(m => m.OffenderId, new { @class = "form-control onlyNumeric", @autofocus = "autofocus", @id = "txtInmateNum", maxlength = "6", Value = string.Empty })
                                    <button id="btnFindOffender" type="button" class="btn btn-primary" name="submitAction" value="Search">
                                        <span>Find Inmate</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>*@

@*<div id="Housing-Manage" class="no-print">
        <div class="row">
            <div class="col-md-4">
                <select name="InmateIdPrefix" class="form-control input-sm" style="display:inline;width:80px;">
                    @foreach (var item in Model.Prefix)
                    {
                        <option value="@item.Value">@item.Text</option>
                    }
                </select>
                <input type="text" name="OffenderId" class="form-control input-sm" placeholder="Offender #" style="display:inline;width:120px;" />
                <button type="button" class="btn btn-success" id="btnFindOffender">
                    <span class="glyphicon glyphicon-search"></span> Find Inmate
                </button>
            </div>
        </div>
    </div>
    <br />*@
@using (Html.BeginForm("Index", "Mosci", FormMethod.Post, new { @id = "Mosci", @class = "form-horizontal" }))
{
    @Html.AntiForgeryToken()

    @* Hidden field for JSON model data (used by JavaScript) *@
    @Html.Hidden("modelJson", "", new { id = "modelJson" })

    //-------------------------------------------------
    <div class="panel panel-primary">
        <div class="panel-heading">
            Schedule Inmate Move&nbsp;&nbsp;-&nbsp;&nbsp;MOSCI
        </div>
        <div class="panel-body">
            <div class="table-responsive">
                <table id="inmateTable" class="table table-bordered table-condensed">
                    <thead class="odrc-header-row">
                        <tr>
                            <td style="width:60px;"></td>
                            <td style="width:150px;">Offender #</td>
                            <td style="width:150px;">Last Name</td>
                            <td style="width:150px;">First Name</td>
                            <td style="width:120px;">From</td>
                            <td style="width:140px;">To</td>
                            <td style="width:140px;">Scheduled Date</td>
                            <td style="width:80px;">Comments</td>
                            <td style="width:80px;">Remove</td>
                            <td style="width:80px;">Delete</td>
                        </tr>
                    </thead>
                    <tbody>
                        @for (int i = 0; i < Model.Inmates.Count; i++)
                        {
                            <tr @if (i == 0) { <text>id="inmate-row-template"</text> }>
                                <td>
                                    @Html.DropDownListFor(m => m.Inmates[i].InmateIdPrefix, Model.PrefixOptions, new { @class = "form-control input-sm", name = "InmateIdPrefix" })
                                    @Html.HiddenFor(m => m.Inmates[i].Recno)
                                </td>
                                <td>@Html.TextBoxFor(m => m.Inmates[i].OffenderId, new { @class = "form-control input-sm onlyNumeric", @maxlength = "6", name = "OffenderId" })</td>
                                <td>@Html.TextBoxFor(m => m.Inmates[i].LastName, new { @class = "form-control input-sm", name = "LastName" })</td>
                                <td>@Html.TextBoxFor(m => m.Inmates[i].FirstName, new { @class = "form-control input-sm", name = "FirstName" })</td>
                                <td>
                                    <select name="FromInstitution" class="form-control input-sm">
                                        <option>MANCINI</option>
                                        @* Populate From-institution list *@
                                    </select>
                                </td>
                                <td>@Html.TextBoxFor(m => m.Inmates[i].SchdInst, new { @class = "form-control input-sm", name = "SchdInst" })</td>
                                <td>@Html.TextBoxFor(m => m.Inmates[i].SchDate, "{0:MM/dd/yyyy}", new { @class = "form-control input-sm", name = "SchDate" })</td>
                                <td>@Html.TextBoxFor(m => m.Inmates[i].Descrl, new { @class = "form-control input-sm", name = "Descrl" })</td>
                                <td class="text-center">@Html.CheckBoxFor(m => m.Inmates[i].IsMarkedForRemoval, new { name = "Remove" })</td>
                                <td class="text-center">@Html.CheckBoxFor(m => m.Inmates[i].IsMarkedForDeletion, new { name = "Delete" })</td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>

            <!-- Add / Remove Row Buttons -->
            <div class="row">
                <div class="col-md-6 col-xs-12">
                    <button type="submit" name="submitAction" value="AddNew" id="btnAddNewInmate" class="btn btn-primary">
                        <span class="glyphicon glyphicon-plus"></span> Add New Inmate
                    </button>
                </div>
                <div class="col-md-6 col-xs-12 text-right">
                    <button type="submit" name="submitAction" value="RemoveSelected" name="btnRemoveInmate" class="btn btn-danger">
                        <span class="glyphicon glyphicon-remove"></span> Remove Inmate
                    </button>
                    <span style="display:inline-block; width:20px;"></span>
                    <button type="submit" name="submitAction" value="DeleteSelected" name="btnDelete" class="btn btn-default">
                        <span class="glyphicon glyphicon-trash"></span> Delete
                    </button>
                </div>
            </div>
            <br />
            <!-- Save / Cancel Buttons -->
            <div class="row text-center">
                <button type="submit" name="submitAction" value="Save" class="btn btn-primary" id="btnSave">
                    <span class="glyphicon glyphicon-floppy-disk"></span> Save
                </button>
                <button type="submit" name="submitAction" value="Cancel" class="btn btn-default" id="btnCancel">
                    <span class="glyphicon glyphicon-remove-circle"></span> Cancel
                </button>
            </div>
        </div>
    </div>



    @section Scripts {
        <!-- Use jQuery UI from CDN -->
        <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"
                integrity="sha256-lSjKY0/srUM9BE3dPm+c4fBo1dky2v27Gdjm2uoZaL0="
                crossorigin="anonymous"></script>

        <!-- Define MyScript object to prevent errors -->
        <script type="text/javascript">
            // Define MyScript object if it doesn't exist
            var MyScript = MyScript || {};

            // Add init method if it doesn't exist
            MyScript.init = MyScript.init || function(options) {
                console.log("MyScript.init called with options:", options);
                window.m_options = options; // Store options globally if needed
            };

            var MOSCI = MOSCI || {};
            MOSCI.MAX_ROWS = 19;

        $(function () {
            // Clone first row and clear inputs when adding a new inmate
            $('#btnAddNewInmate').on('click', function (e) {
                e.preventDefault();

                // Check if we've reached the maximum number of rows
                var currentRowCount = $('#inmateTable tbody tr').length;
                if (currentRowCount >= MOSCI.MAX_ROWS) {
                    alert('Maximum number of rows (' + MOSCI.MAX_ROWS + ') reached. Cannot add more rows.');
                    return null;
                }

                // Clone the template row
                var $newRow = $('#inmate-row-template').clone();

                // Generate a unique ID for the new row
                var rowId = 'inmate-row-' + new Date().getTime();
                $newRow.attr('id', rowId);

                // Clear all input values
                $newRow.find('input[type="text"]').val('');
                $newRow.find('select').prop('selectedIndex', 0);
                $newRow.find('input[type="checkbox"]').prop('checked', false);

                // Append the new row to the table
                $('#inmateTable tbody').append($newRow);

                // Scroll to the new row
                $('html, body').animate({
                    scrollTop: $newRow.offset().top - 100
                }, 500);
            });

            // Handle remove inmate button
            $('button[name="btnRemoveInmate"]').on('click', function() {
                var hasChecked = false;

                $('#inmateTable tbody tr').each(function() {
                    var $row = $(this);
                    if ($row.find('input[name="Remove"]').is(':checked')) {
                        $row.remove();
                        hasChecked = true;
                    }
                });

                if (!hasChecked) {
                    alert('Please select at least one inmate to remove.');
                }
            });

            // Handle delete button
            $('button[name="btnDelete"]').on('click', function() {
                var hasChecked = false;

                $('#inmateTable tbody tr').each(function() {
                    var $row = $(this);
                    if ($row.find('input[name="Delete"]').is(':checked')) {
                        $row.remove();
                        hasChecked = true;
                    }
                });

                if (!hasChecked) {
                    alert('Please select at least one inmate to delete.');
                }
            });

            // Handle cancel button
            $('#btnCancel').on('click', function() {
                if (confirm('Are you sure you want to cancel? Any unsaved changes will be lost.')) {
                    window.location.href = '@Url.Action("Index", "Home", new { area = "" })';
                }
            });


            var updateButtonState = function () {
                var rowCount = $('#inmateTable tbody tr').length;
                $('#btnAddNewInmate').prop('disabled', rowCount >= MOSCI.MAX_ROWS);
                console.log('Row count: ' + rowCount + ', Add button ' + (rowCount >= MOSCI.MAX_ROWS ? 'disabled' : 'enabled'));
            };


            updateButtonState();

            //    $("#txtInmateNum").keyup(function (event) {
                 // if (event.keyCode == 13) {
            //        $("#btnFindOffender").click();

                 // }
            //});

            MyScript.init({
                MOSCI: '@Url.Action("Mosci", "Mosci", new { area = "Transfers" })',
                    GetEmployeeInfoByOaksId: '@Url.Action("GetEmployeeInfoByOaksId", "Mosci", new { area = "Transfers" })'
                });


            //Inmate Search button
            //$("#btnFindOffender").click(function (e) {
            //    var oidLetter = $("#InmateIdPrefix").val();
            //    var oidNumber = $("#txtInmateNum").val();

            //    var offenderNumber = '';
            //    if (oidNumber !== '') {
            //        offenderNumber = "?searchOid=" + oidLetter + oidNumber;
            //    } else {
            //        e.preventDefault();
            //        $('#divErrorMessage').show();
            //        $('#ErrorMessage').text("Please enter Inmate number");
            //        return false;
            //    }
            //    //window.location.replace(m_options.MOSCI + offenderNumber);
            //});

            // Find an offender
            //$("#btnFindOffender").click(function (event) {
            //    console.log('Finding offender');

            //    var prefix = $('#InmateIdPrefix').val();
            //    var offenderId = $('#txtInmateNum').val();

            //    var offenderNumber = '';
            //    if (!offenderId) {
            //        alert('Please enter an Offender ID.');
            //        return;
            //    }
            //    else {
            //        offenderNumber = prefix + offenderId;

            //    }

            //    // Show loading indicator
            //    $('#btnFindOffender').prop('disabled', true).html('<span class="glyphicon glyphicon-refresh glyphicon-spin"></span> Searching...');

            //    // Clear the table except for the first empty row
            //    MOSCI.clearTable();

            //    // Make AJAX call to find inmate
            //    $.ajax({
            //        url: 'Transfers/Mosci/Mosci',
            //        type: 'GET',
            //        data: { offenderId: offenderNumber },
            //        success: function (result) {
            //            // Reset button
            //            $('#btnFindOffender').prop('disabled', false).html('Search');

            //            // Log the entire result for debugging
            //            console.log('Server response:', result);

            //            if (result.success) {
            //                var inmates = result.inmates;
            //                var count = result.count;

            //                // Check if the first row is empty (default row)
            //                var $firstRow = $('#inmateTable tbody tr:first');
            //                var isFirstRowEmpty = $firstRow.find('input[name="OffenderId"]').val() === '';

            //                // Loop through all matching inmates
            //                for (var i = 0; i < inmates.length; i++) {
            //                    var inmate = inmates[i];

            //                    // For the first inmate, use the first row if it's empty
            //                    if (i === 0 && isFirstRowEmpty) {
            //                        console.log('Using the first empty row for inmate #1');

            //                        // Find the select element for InmateIdPrefix
            //                        var $select = $firstRow.find('select[name="InmateIdPrefix"]');

            //                        // Clear any existing selected options
            //                        $select.find('option').prop('selected', false);

            //                        // Select the option with the matching value
            //                        $select.find('option[value="' + inmate.inmateIdPrefix + '"]').prop('selected', true);

            //                        // Set other field values
            //                        $firstRow.find('input[name="OffenderId"]').val(inmate.offenderId);
            //                        $firstRow.find('input[name="LastName"]').val(inmate.lastName);
            //                        $firstRow.find('input[name="FirstName"]').val(inmate.firstName);
            //                        $firstRow.find('input[name="SchdInst"]').val(inmate.schdInst);
            //                        $firstRow.find('input[name="SchDate"]').val(inmate.schDate);
            //                        $firstRow.find('input[name="Descrl"]').val(inmate.descrl);
            //                    } else {
            //                        // Check if we've reached the maximum number of rows
            //                        if ($('#inmateTable tbody tr').length >= MOSCI.MAX_ROWS) {
            //                            alert('Maximum number of rows (' + MOSCI.MAX_ROWS + ') reached. Cannot add more inmates.');
            //                            break;
            //                        }

            //                        // Add a new row with the inmate data
            //                        var $newRow = MOSCI.addNewInmate();

            //                        if ($newRow) {
            //                            // Find the select element for InmateIdPrefix
            //                            var $select = $newRow.find('select[name="InmateIdPrefix"]');

            //                            // Clear any existing selected options
            //                            $select.find('option').prop('selected', false);

            //                            // Select the option with the matching value
            //                            $select.find('option[value="' + inmate.inmateIdPrefix + '"]').prop('selected', true);

            //                            // Set other field values
            //                            $newRow.find('input[name="OffenderId"]').val(inmate.offenderId);
            //                            $newRow.find('input[name="LastName"]').val(inmate.lastName);
            //                            $newRow.find('input[name="FirstName"]').val(inmate.firstName);
            //                            $newRow.find('input[name="SchdInst"]').val(inmate.schdInst);
            //                            $newRow.find('input[name="SchDate"]').val(inmate.schDate);
            //                            $newRow.find('input[name="Descrl"]').val(inmate.descrl);

            //                            console.log('Inmate #' + (i + 1) + ' added to a new row');
            //                        }
            //                    }
            //                }

            //                // Show success message
            //                alert(result.message);
            //            } else {
            //                // Show error message
            //                alert('Error: ' + result.message);
            //            }
            //        },
            //        error: function (xhr, status, error) {
            //            // Reset button
            //            $('#btnFindOffender').prop('disabled', false).html('Search');

            //            // Show error message
            //            console.error('AJAX Error:', status, error);
            //            alert('Error finding inmate. See console for details.');
            //        }
            //    });
            //};

            $('#btnFindOffender').click(function (e) {
                e.preventDefault();
                var prefix = $('select[name="InmateIdPrefix"]').first().val();
                var offenderId = $('#txtInmateNum').val();

                // Validate offenderId
                if (!offenderId || offenderId.trim() === '') {
                    alert('Please enter an Offender ID.');
                    return;
                }

                // Trim any whitespace
                offenderId = offenderId.trim();

                // Concatenate the prefix with the offender ID for searching
                var searchOffenderId = prefix + offenderId;

                // Log the values for debugging
                console.log('Searching for inmate with prefix:', prefix, 'and offenderId:', offenderId);
                console.log('Combined search ID:', searchOffenderId);

                $(this).prop('disabled', true)
                       .html('<span class="glyphicon glyphicon-refresh glyphicon-refresh-animate"></span> Searching…');

                MOSCI.clearTable();

                $.ajax({
                    url: '@Url.Action("FindInmate", "Mosci", new { area = "Transfers" })',
                    type: 'POST',
                    data: { searchOffenderId: searchOffenderId },
                    success: function (result) {
                        $('#btnFindOffender').prop('disabled', false).html('Find Inmate');
                        console.log('Server response:', result);

                        if (result.success) {
                            var inmates = result.inmates;
                            var $first = $('#inmateTable tbody tr').first();
                            var isEmpty = !$first.find('input[name="OffenderId"]').val();

                            inmates.forEach(function (inmate, i) {
                                var $row;

                                if (i === 0 && isEmpty) {
                                    $row = $first;
                                } else {
                                    // Use the MOSCI.addNewInmate function to add a new row
                                    $row = MOSCI.addNewInmate();
                                }

                                if (!$row || $row.length === 0) {
                                    console.error('Failed to get row for inmate #' + (i + 1));
                                    return;
                                }

                                var $sel = $row.find('select[name="InmateIdPrefix"]');
                                $sel.find('option').prop('selected', false);
                                $sel.find('option[value="' + inmate.inmateIdPrefix + '"]').prop('selected', true);

                                $row.find('input[name="OffenderId"]').val(inmate.combinedOffenderId);
                                $row.find('input[name="LastName"]').val(inmate.lastName);
                                $row.find('input[name="FirstName"]').val(inmate.firstName);
                                $row.find('input[name="SchdInst"]').val(inmate.schdInst);
                                $row.find('input[name="SchDate"]').val(inmate.schDate);
                                $row.find('input[name="Descrl"]').val(inmate.descrl);
                            });

                            updateButtonState();
                            alert(result.message);
                        } else {
                            alert('Error: ' + result.message);
                        }
                    },
                    error: function (xhr, status, error) {
                        $('#btnFindOffender').prop('disabled', false).html('Find Inmate');
                        console.error('AJAX Error:', status, error);
                        alert('Error finding inmate. See console for details.');
                    }
                });
            });

            $("#btnSearch").click(function (event) {
                event.preventDefault();
                var oaksId = $("#OaksId").val();
                $.ajax({
                    url: window.m_options ? window.m_options.GetEmployeeInfoByOaksId : '@Url.Action("GetEmployeeInfoByOaksId", "Mosci", new { area = "Transfers" })',
                    type: 'GET',
                    data: { oaksId: oaksId },
                    dataType: 'json',
                    success: function (response) {
                        if (response.Message == "No Record") {
                            alert("Please provide correct Oaks Id!");
                            return false;
                        }
                        $("#hdnOaksId").val(oaksId);
                        $("#FirstName").val(response.FirstName.toUpperCase());
                        $("#LastName").val(response.LastName.toUpperCase());
                        $("#JobTitle").val(response.JobTitle.toUpperCase());

                        // Multiselect functionality
                        $('#SelectedInsts').multiselect('deselectAll', false);
                        $('#SelectedInsts').next('.btn-group').find('.multiselect.dropdown-toggle').attr('title', 'None Selected');
                        $('#SelectedInsts').next('.btn-group').find('.multiselect-selected-text').html('None selected');
                        var selectedValues = response.SelectedInsts;
                        $('#SelectedInsts option').each(function () {
                            var optionValue = $(this).val();

                            if (selectedValues.includes(optionValue)) {
                                var res = $("#SelectedInsts").val();
                                $("#SelectedInsts").prop('selected', true);
                                $("#SelectedInsts").multiselect('select', optionValue);
                            }
                        });
                    },
                    error: function (xhr, status, error) {
                        alert("Error while getting Employee Info");
                        $("#result").html("<p>Error: " + error + "</p>");
                    }
                });
            });
        });
</script>

        <!-- Common.js is not needed since we defined MyScript above -->
        <!-- <script type="text/javascript" src="~/Areas/Rh/Scripts/Common.js"></script> -->
    }
}
